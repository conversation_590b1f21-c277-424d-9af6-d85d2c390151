package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 状态转换请求DTO
 * 
 * 用于封装增值交付单状态转换的请求参数
 *
 * <AUTHOR>
 * @date 2025-08-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("状态转换请求DTO")
public class StatusTransitionRequest {

    /**
     * 交付单编号
     */
    @NotBlank(message = "交付单编号不能为空")
    @ApiModelProperty(value = "交付单编号", required = true, example = "VAD2508051430001A1C")
    private String deliveryOrderNo;

    /**
     * 目标状态
     */
    @NotBlank(message = "目标状态不能为空")
    @ApiModelProperty(value = "目标状态", required = true, 
                     example = "SUBMITTED_PENDING_DELIVERY",
                     allowableValues = "DRAFT,SAVED_PENDING_SUBMIT,SUBMITTED_PENDING_DELIVERY,PENDING_CONFIRMATION,CONFIRMED_PENDING_DEDUCTION,DEDUCTION_PROCESSING,DEDUCTION_PENDING_CONFIRMATION,DEDUCTION_COMPLETED,DEDUCTION_EXCEPTION,DEDUCTION_CLOSED,PENDING_REVIEW,DELIVERY_PROCESSING,DELIVERY_COMPLETED,DELIVERY_EXCEPTION,DELIVERY_CLOSED")
    private String targetStatus;

    /**
     * 操作类型代码（对应图中的操作）
     */
    @ApiModelProperty(value = "操作类型代码", 
                     example = "added:submit",
                     allowableValues = "added:submit,added:newSubmit,added:deliverSubmit,added:change,added:receive,added:deduction,added:deductionSubmit,added:deliver,added:supply,added:handleException,added:check,added:closeDeliver,added:back")
    private String operationCode;

    /**
     * 转换原因或备注
     */
    @ApiModelProperty(value = "转换原因或备注", example = "用户提交交付单")
    private String reason;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 是否强制转换（跳过某些业务验证）
     */
    @ApiModelProperty(value = "是否强制转换", example = "false")
    private Boolean forceTransition = false;

    /**
     * 附加数据（JSON格式，用于存储转换时的额外信息）
     */
    @ApiModelProperty(value = "附加数据（JSON格式）")
    private String additionalData;
}
