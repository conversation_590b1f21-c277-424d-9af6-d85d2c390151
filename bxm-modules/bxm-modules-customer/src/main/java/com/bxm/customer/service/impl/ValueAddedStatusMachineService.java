package com.bxm.customer.service.impl;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedStatusHistory;
import com.bxm.customer.domain.dto.StatusTransitionRequest;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

/**
 * 增值交付单状态机服务
 * 
 * 负责处理状态转换的核心逻辑，包括：
 * 1. 状态转换验证
 * 2. 业务规则检查
 * 3. 状态转换执行
 * 4. 转换历史记录
 *
 * <AUTHOR>
 * @date 2025-08-10
 */
@Slf4j
@Service
public class ValueAddedStatusMachineService {

    /**
     * 操作代码与状态转换的映射关系
     * 基于图中的状态流转定义
     */
    private static final Map<String, StatusTransition> OPERATION_TRANSITIONS = new HashMap<>();

    static {
        // 提交相关操作
        OPERATION_TRANSITIONS.put("added:submit", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.DRAFT, ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT));
        OPERATION_TRANSITIONS.put("added:newSubmit", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT, ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY));
        OPERATION_TRANSITIONS.put("added:deliverSubmit", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION));

        // 确认和变更操作
        OPERATION_TRANSITIONS.put("added:change", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION, ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION));
        OPERATION_TRANSITIONS.put("added:receive", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION, ValueAddedDeliveryOrderStatus.PENDING_REVIEW));

        // 扣款相关操作
        OPERATION_TRANSITIONS.put("added:deduction", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION, ValueAddedDeliveryOrderStatus.DEDUCTION_PENDING_CONFIRMATION));
        OPERATION_TRANSITIONS.put("added:deductionSubmit", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.DEDUCTION_PENDING_CONFIRMATION, ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED));

        // 交付相关操作
        OPERATION_TRANSITIONS.put("added:deliver", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION));
        OPERATION_TRANSITIONS.put("added:supply", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION));

        // 异常处理操作
        OPERATION_TRANSITIONS.put("added:handleException", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.DELIVERY_PROCESSING, ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION));

        // 确认操作
        OPERATION_TRANSITIONS.put("added:check", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION, ValueAddedDeliveryOrderStatus.PENDING_REVIEW));

        // 关闭操作
        OPERATION_TRANSITIONS.put("added:closeDeliver", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION, ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED));

        // 退回操作
        OPERATION_TRANSITIONS.put("added:back", 
            new StatusTransition(ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY, ValueAddedDeliveryOrderStatus.SAVED_PENDING_SUBMIT));
    }

    /**
     * 验证状态转换是否合法
     *
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @param operationCode 操作代码
     * @return 验证结果
     */
    public StatusTransitionResult validateTransition(ValueAddedDeliveryOrderStatus currentStatus, 
                                                   ValueAddedDeliveryOrderStatus targetStatus, 
                                                   String operationCode) {
        try {
            log.info("Validating status transition from {} to {} with operation {}", 
                    currentStatus.getCode(), targetStatus.getCode(), operationCode);

            // 1. 检查状态是否相同
            if (currentStatus == targetStatus) {
                return StatusTransitionResult.failure("当前状态与目标状态相同，无需转换");
            }

            // 2. 检查是否为终态
            if (currentStatus.isFinalStatus()) {
                return StatusTransitionResult.failure("当前状态为终态，不允许转换");
            }

            // 3. 检查基础转换规则
            if (!currentStatus.canTransitionTo(targetStatus)) {
                return StatusTransitionResult.failure(
                    String.format("不允许从状态 %s 转换到 %s", currentStatus.getDescription(), targetStatus.getDescription()));
            }

            // 4. 检查操作代码对应的转换规则（如果提供了操作代码）
            if (operationCode != null && !operationCode.trim().isEmpty()) {
                StatusTransition expectedTransition = OPERATION_TRANSITIONS.get(operationCode);
                if (expectedTransition != null) {
                    if (expectedTransition.getFromStatus() != currentStatus || 
                        expectedTransition.getToStatus() != targetStatus) {
                        return StatusTransitionResult.failure(
                            String.format("操作代码 %s 不支持从 %s 到 %s 的转换", 
                                        operationCode, currentStatus.getDescription(), targetStatus.getDescription()));
                    }
                }
            }

            log.info("Status transition validation passed");
            return StatusTransitionResult.success("状态转换验证通过");

        } catch (Exception e) {
            log.error("Status transition validation failed", e);
            return StatusTransitionResult.failure("状态转换验证失败: " + e.getMessage());
        }
    }

    /**
     * 执行状态转换
     *
     * @param order 交付单对象
     * @param request 转换请求
     * @return 转换结果
     */
    public StatusTransitionResult executeTransition(ValueAddedDeliveryOrder order, StatusTransitionRequest request) {
        try {
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            ValueAddedDeliveryOrderStatus targetStatus = ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());

            if (currentStatus == null) {
                return StatusTransitionResult.failure("当前状态无效: " + order.getStatus());
            }
            if (targetStatus == null) {
                return StatusTransitionResult.failure("目标状态无效: " + request.getTargetStatus());
            }

            // 1. 验证状态转换规则
            if (!request.getForceTransition()) {
                StatusTransitionResult validationResult = validateTransition(currentStatus, targetStatus, request.getOperationCode());
                if (!validationResult.isSuccess()) {
                    return validationResult;
                }

                // 2. 验证业务规则
                StatusTransitionResult businessValidationResult = validateBusinessRules(order, targetStatus);
                if (!businessValidationResult.isSuccess()) {
                    return businessValidationResult;
                }
            }

            // 3. 执行状态转换
            String oldStatus = order.getStatus();
            order.setStatus(targetStatus.getCode());
            order.setUpdateTime(LocalDateTime.now());

            // 4. 记录状态变更历史
            recordStatusHistory(order, oldStatus, targetStatus.getCode(), request);

            // 5. 执行后置动作
            executePostActions(order, currentStatus, targetStatus);

            // 6. 记录转换日志
            log.info("Status transition executed successfully: {} -> {} for order {} with operation {}",
                    oldStatus, targetStatus.getCode(), order.getDeliveryOrderNo(), request.getOperationCode());

            return StatusTransitionResult.success("状态转换执行成功")
                    .withOldStatus(oldStatus)
                    .withNewStatus(targetStatus.getCode());

        } catch (Exception e) {
            log.error("Status transition execution failed for order {}", order.getDeliveryOrderNo(), e);
            return StatusTransitionResult.failure("状态转换执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定状态的可用转换操作
     *
     * @param currentStatus 当前状态
     * @return 可用的操作代码集合
     */
    public Set<String> getAvailableOperations(ValueAddedDeliveryOrderStatus currentStatus) {
        Set<String> operations = new HashSet<>();
        for (Map.Entry<String, StatusTransition> entry : OPERATION_TRANSITIONS.entrySet()) {
            if (entry.getValue().getFromStatus() == currentStatus) {
                operations.add(entry.getKey());
            }
        }
        return operations;
    }

    /**
     * 执行业务规则验证
     *
     * @param order 交付单对象
     * @param targetStatus 目标状态
     * @return 验证结果
     */
    private StatusTransitionResult validateBusinessRules(ValueAddedDeliveryOrder order,
                                                        ValueAddedDeliveryOrderStatus targetStatus) {
        try {
            // 根据目标状态执行特定的业务规则验证
            switch (targetStatus) {
                case SUBMITTED_PENDING_DELIVERY:
                    // 提交交付时需要验证必要信息是否完整
                    if (order.getCustomerName() == null || order.getCustomerName().trim().isEmpty()) {
                        return StatusTransitionResult.failure("客户名称不能为空");
                    }
                    if (order.getValueAddedItemTypeId() == null) {
                        return StatusTransitionResult.failure("增值事项类型不能为空");
                    }
                    break;

                case DEDUCTION_PROCESSING:
                    // 开始扣款时需要验证扣款相关信息
                    if (order.getCustomerId() == null) {
                        return StatusTransitionResult.failure("客户ID不能为空，无法执行扣款");
                    }
                    break;

                case DELIVERY_COMPLETED:
                    // 交付完成时需要验证交付材料
                    // 这里可以添加检查交付材料是否上传的逻辑
                    break;

                case DEDUCTION_COMPLETED:
                    // 扣款完成时的验证
                    break;

                default:
                    // 其他状态暂无特殊业务规则
                    break;
            }

            return StatusTransitionResult.success("业务规则验证通过");

        } catch (Exception e) {
            log.error("Business rule validation failed for order {}", order.getDeliveryOrderNo(), e);
            return StatusTransitionResult.failure("业务规则验证失败: " + e.getMessage());
        }
    }

    /**
     * 执行状态转换后的后置动作
     *
     * @param order 交付单对象
     * @param oldStatus 原状态
     * @param newStatus 新状态
     */
    private void executePostActions(ValueAddedDeliveryOrder order,
                                  ValueAddedDeliveryOrderStatus oldStatus,
                                  ValueAddedDeliveryOrderStatus newStatus) {
        try {
            log.info("Executing post actions for status transition: {} -> {} for order {}",
                    oldStatus.getCode(), newStatus.getCode(), order.getDeliveryOrderNo());

            // 根据新状态执行相应的后置动作
            switch (newStatus) {
                case SUBMITTED_PENDING_DELIVERY:
                    // 提交交付后可能需要发送通知
                    log.info("Order {} submitted for delivery, notification may be sent", order.getDeliveryOrderNo());
                    break;

                case DEDUCTION_PROCESSING:
                    // 开始扣款处理
                    log.info("Order {} deduction processing started", order.getDeliveryOrderNo());
                    break;

                case DEDUCTION_COMPLETED:
                    // 扣款完成后的处理
                    log.info("Order {} deduction completed", order.getDeliveryOrderNo());
                    break;

                case DELIVERY_COMPLETED:
                    // 交付完成后的处理
                    log.info("Order {} delivery completed", order.getDeliveryOrderNo());
                    break;

                default:
                    // 其他状态的后置动作
                    break;
            }

        } catch (Exception e) {
            log.error("Post action execution failed for order {}", order.getDeliveryOrderNo(), e);
            // 后置动作失败不影响状态转换的成功
        }
    }

    /**
     * 记录状态变更历史
     *
     * @param order 交付单对象
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param request 转换请求
     */
    private void recordStatusHistory(ValueAddedDeliveryOrder order, String oldStatus, String newStatus, StatusTransitionRequest request) {
        try {
            ValueAddedStatusHistory history = ValueAddedStatusHistory.builder()
                    .deliveryOrderNo(order.getDeliveryOrderNo())
                    .deliveryOrderId(order.getId())
                    .oldStatus(oldStatus)
                    .newStatus(newStatus)
                    .operationCode(request.getOperationCode())
                    .reason(request.getReason())
                    .operatorId(request.getOperatorId())
                    .operatorName(request.getOperatorName())
                    .forceTransition(request.getForceTransition())
                    .additionalData(request.getAdditionalData())
                    .createTime(LocalDateTime.now())
                    .remark("状态转换: " + oldStatus + " -> " + newStatus)
                    .build();

            // 这里应该调用历史记录的保存服务，暂时只记录日志
            log.info("Status history recorded: {} -> {} for order {} by operator {}",
                    oldStatus, newStatus, order.getDeliveryOrderNo(), request.getOperatorName());

        } catch (Exception e) {
            log.error("Failed to record status history for order {}", order.getDeliveryOrderNo(), e);
            // 历史记录失败不影响状态转换的成功
        }
    }

    /**
     * 状态转换定义内部类
     */
    private static class StatusTransition {
        private final ValueAddedDeliveryOrderStatus fromStatus;
        private final ValueAddedDeliveryOrderStatus toStatus;

        public StatusTransition(ValueAddedDeliveryOrderStatus fromStatus, ValueAddedDeliveryOrderStatus toStatus) {
            this.fromStatus = fromStatus;
            this.toStatus = toStatus;
        }

        public ValueAddedDeliveryOrderStatus getFromStatus() {
            return fromStatus;
        }

        public ValueAddedDeliveryOrderStatus getToStatus() {
            return toStatus;
        }
    }

    /**
     * 状态转换结果类
     */
    public static class StatusTransitionResult {
        private boolean success;
        private String message;
        private String oldStatus;
        private String newStatus;

        private StatusTransitionResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public static StatusTransitionResult success(String message) {
            return new StatusTransitionResult(true, message);
        }

        public static StatusTransitionResult failure(String message) {
            return new StatusTransitionResult(false, message);
        }

        public StatusTransitionResult withOldStatus(String oldStatus) {
            this.oldStatus = oldStatus;
            return this;
        }

        public StatusTransitionResult withNewStatus(String newStatus) {
            this.newStatus = newStatus;
            return this;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getOldStatus() { return oldStatus; }
        public String getNewStatus() { return newStatus; }
    }
}
