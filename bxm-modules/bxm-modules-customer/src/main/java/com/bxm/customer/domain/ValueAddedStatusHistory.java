package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 增值交付单状态变更历史记录
 * 
 * 用于记录交付单状态的每次变更，包括变更前后状态、操作人、操作时间等信息
 *
 * <AUTHOR>
 * @date 2025-08-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("value_added_status_history")
@ApiModel("增值交付单状态变更历史")
public class ValueAddedStatusHistory {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 交付单编号
     */
    @TableField("delivery_order_no")
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /**
     * 交付单ID
     */
    @TableField("delivery_order_id")
    @ApiModelProperty(value = "交付单ID")
    private Long deliveryOrderId;

    /**
     * 变更前状态
     */
    @TableField("old_status")
    @ApiModelProperty(value = "变更前状态")
    private String oldStatus;

    /**
     * 变更后状态
     */
    @TableField("new_status")
    @ApiModelProperty(value = "变更后状态")
    private String newStatus;

    /**
     * 操作代码
     */
    @TableField("operation_code")
    @ApiModelProperty(value = "操作代码")
    private String operationCode;

    /**
     * 变更原因
     */
    @TableField("reason")
    @ApiModelProperty(value = "变更原因")
    private String reason;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    @ApiModelProperty(value = "操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @TableField("operator_name")
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;

    /**
     * 是否强制转换
     */
    @TableField("force_transition")
    @ApiModelProperty(value = "是否强制转换")
    private Boolean forceTransition;

    /**
     * 附加数据（JSON格式）
     */
    @TableField("additional_data")
    @ApiModelProperty(value = "附加数据（JSON格式）")
    private String additionalData;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 备注
     */
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;
}
