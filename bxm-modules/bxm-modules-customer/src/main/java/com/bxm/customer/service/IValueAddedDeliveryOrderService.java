package com.bxm.customer.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusTransitionRequest;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.service.impl.ValueAddedStatusMachineService;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 增值交付单Service接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IValueAddedDeliveryOrderService extends IService<ValueAddedDeliveryOrder>
{

    /**
     * 新增或更新增值交付单
     *
     * 支持以下场景：
     * 1. 新增：不提供ID和交付单编号，系统自动生成交付单编号
     * 2. 更新：提供ID或交付单编号，更新现有记录
     * 3. 智能判断：根据客户信息和增值事项判断是否存在重复记录
     *
     * @param orderVO 增值交付单VO对象，包含完整的验证注解和业务字段
     * @return 操作结果，包含交付单编号和操作类型信息
     * @throws IllegalArgumentException 当参数验证失败时抛出（如必填字段为空、格式不正确等）
     * @throws RuntimeException 当业务处理失败时抛出（如数据库操作失败等）
     */
    ValueAddedDeliveryOrder upsert(@Valid @NotNull ValueAddedDeliveryOrderVO orderVO);

    /**
     * 根据交付单编号查询增值交付单
     *
     * @param deliveryOrderNo 交付单编号
     * @return 增值交付单对象，如果不存在则返回null
     */
    ValueAddedDeliveryOrder getByDeliveryOrderNo(String deliveryOrderNo);

    /**
     * 根据客户ID和增值事项查询增值交付单
     *
     * @param customerId 客户ID
     * @param valueAddedItemType 增值事项类型
     * @return 增值交付单对象，如果不存在则返回null
     */
    ValueAddedDeliveryOrder getByCustomerIdAndItemType(Long customerId, Integer valueAddedItemType);

    /**
     * 增值交付单条件查询
     *
     * 所有条件在 Service 中动态拼接
     *
     * @param query 查询参数
     * @return 结果列表
     */
    List<ValueAddedDeliveryOrder> query(DeliveryOrderQuery query);

    /**
     * 增值交付单条件查询（返回VO对象）
     *
     * 所有条件在 Service 中动态拼接，返回包含itemName映射的VO对象
     *
     * @param query 查询参数
     * @return VO结果列表，包含itemName字段映射
     */
    List<ValueAddedDeliveryOrderVO> queryVO(DeliveryOrderQuery query);

    /**
     * 修改增值交付单状态
     *
     * 支持完整的状态机转换，包括：
     * 1. 状态转换验证
     * 2. 业务规则检查
     * 3. 状态转换执行
     * 4. 转换历史记录
     *
     * @param request 状态转换请求，包含交付单编号、目标状态、操作代码等信息
     * @return 状态转换结果，包含转换是否成功、错误信息等
     * @throws IllegalArgumentException 当参数验证失败时抛出
     * @throws RuntimeException 当状态转换失败时抛出
     */
    ValueAddedStatusMachineService.StatusTransitionResult modifyStatus(@Valid @NotNull StatusTransitionRequest request);
}
