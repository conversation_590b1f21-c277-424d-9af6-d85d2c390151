package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 增值交付单状态枚举
 * 
 * 状态流程说明：
 * 1. 基础流程：DRAFT → PENDING_CONFIRMATION → CONFIRMED_PENDING_DEDUCTION → DEDUCTION_COMPLETED
 * 2. 扣款异常流程：DEDUCTION_EXCEPTION → DEDUCTION_CLOSED
 * 3. 交付流程：SAVED_PENDING_SUBMIT → SUBMITTED_PENDING_DELIVERY → DELIVERY_COMPLETED
 * 4. 交付异常流程：DELIVERY_EXCEPTION → DELIVERY_CLOSED
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@Getter
@AllArgsConstructor
public enum ValueAddedDeliveryOrderStatus {

    /**
     * 草稿状态 - 初始状态
     */
    DRAFT("DRAFT", "草稿"),

    /**
     * 已交付待确认
     */
    PENDING_CONFIRMATION("PENDING_CONFIRMATION", "已交付待确认"),

    /**
     * 已确认待扣款
     */
    CONFIRMED_PENDING_DEDUCTION("CONFIRMED_PENDING_DEDUCTION", "已确认待扣款"),

    /**
     * 扣款中
     */
    DEDUCTION_PROCESSING("DEDUCTION_PROCESSING", "扣款中"),

    /**
     * 已扣款待确认
     */
    DEDUCTION_PENDING_CONFIRMATION("DEDUCTION_PENDING_CONFIRMATION", "已扣款待确认"),

    /**
     * 已扣款 - 正常完成状态
     */
    DEDUCTION_COMPLETED("DEDUCTION_COMPLETED", "已扣款"),

    /**
     * 扣款异常
     */
    DEDUCTION_EXCEPTION("DEDUCTION_EXCEPTION", "扣款异常"),

    /**
     * 关闭扣款
     */
    DEDUCTION_CLOSED("DEDUCTION_CLOSED", "关闭扣款"),

    /**
     * 已保存待提交
     */
    SAVED_PENDING_SUBMIT("SAVED_PENDING_SUBMIT", "已保存待提交"),

    /**
     * 已提交待交付
     */
    SUBMITTED_PENDING_DELIVERY("SUBMITTED_PENDING_DELIVERY", "已提交待交付"),

    /**
     * 待确认 - 通用确认状态
     */
    PENDING_REVIEW("PENDING_REVIEW", "待确认"),

    /**
     * 交付中
     */
    DELIVERY_PROCESSING("DELIVERY_PROCESSING", "交付中"),

    /**
     * 交付完成
     */
    DELIVERY_COMPLETED("DELIVERY_COMPLETED", "交付完成"),

    /**
     * 交付异常
     */
    DELIVERY_EXCEPTION("DELIVERY_EXCEPTION", "交付异常"),

    /**
     * 关闭交付
     */
    DELIVERY_CLOSED("DELIVERY_CLOSED", "关闭交付");

    /**
     * 状态代码（字符串值）
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态代码获取枚举
     *
     * @param code 状态代码
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ValueAddedDeliveryOrderStatus getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        for (ValueAddedDeliveryOrderStatus status : values()) {
            if (status.getCode().equals(code.trim())) {
                return status;
            }
        }
        return null;
    }

    /**
     * 验证状态代码是否有效
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取默认初始状态
     *
     * @return 默认状态（草稿）
     */
    public static ValueAddedDeliveryOrderStatus getDefaultStatus() {
        return DRAFT;
    }

    /**
     * 判断是否为终态（不可再变更的状态）
     *
     * @return 是否为终态
     */
    public boolean isFinalStatus() {
        return this == DEDUCTION_COMPLETED ||
               this == DEDUCTION_CLOSED ||
               this == DELIVERY_COMPLETED ||
               this == DELIVERY_CLOSED;
    }

    /**
     * 判断是否为异常状态
     *
     * @return 是否为异常状态
     */
    public boolean isExceptionStatus() {
        return this == DEDUCTION_EXCEPTION || this == DELIVERY_EXCEPTION;
    }

    /**
     * 判断是否为处理中状态
     *
     * @return 是否为处理中状态
     */
    public boolean isProcessingStatus() {
        return this == DEDUCTION_PROCESSING || this == DELIVERY_PROCESSING;
    }

    /**
     * 判断是否可以转换到目标状态
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == null || this == targetStatus) {
            return false;
        }

        // 终态不能再转换
        if (this.isFinalStatus()) {
            return false;
        }

        // 根据业务规则定义允许的状态转换
        switch (this) {
            case DRAFT:
                return targetStatus == SAVED_PENDING_SUBMIT;

            case SAVED_PENDING_SUBMIT:
                return targetStatus == SUBMITTED_PENDING_DELIVERY ||
                       targetStatus == DRAFT;

            case SUBMITTED_PENDING_DELIVERY:
                return targetStatus == PENDING_CONFIRMATION ||
                       targetStatus == DELIVERY_PROCESSING ||
                       targetStatus == DELIVERY_EXCEPTION ||
                       targetStatus == SAVED_PENDING_SUBMIT;

            case PENDING_CONFIRMATION:
                return targetStatus == CONFIRMED_PENDING_DEDUCTION ||
                       targetStatus == PENDING_REVIEW ||
                       targetStatus == SAVED_PENDING_SUBMIT;

            case CONFIRMED_PENDING_DEDUCTION:
                return targetStatus == DEDUCTION_PROCESSING ||
                       targetStatus == DEDUCTION_EXCEPTION ||
                       targetStatus == PENDING_REVIEW;

            case DEDUCTION_PROCESSING:
                return targetStatus == DEDUCTION_PENDING_CONFIRMATION ||
                       targetStatus == DEDUCTION_EXCEPTION;

            case DEDUCTION_PENDING_CONFIRMATION:
                return targetStatus == DEDUCTION_COMPLETED ||
                       targetStatus == PENDING_REVIEW;

            case PENDING_REVIEW:
                return targetStatus == CONFIRMED_PENDING_DEDUCTION ||
                       targetStatus == DEDUCTION_COMPLETED ||
                       targetStatus == DELIVERY_COMPLETED;

            case DELIVERY_PROCESSING:
                return targetStatus == DELIVERY_COMPLETED ||
                       targetStatus == DELIVERY_EXCEPTION;

            case DEDUCTION_EXCEPTION:
                return targetStatus == DEDUCTION_CLOSED ||
                       targetStatus == DEDUCTION_PROCESSING;

            case DELIVERY_EXCEPTION:
                return targetStatus == DELIVERY_CLOSED ||
                       targetStatus == DELIVERY_PROCESSING;

            default:
                return false;
        }
    }

    /**
     * 获取当前状态可以转换到的所有目标状态
     *
     * @return 可转换的目标状态列表
     */
    public java.util.List<ValueAddedDeliveryOrderStatus> getAvailableTransitions() {
        java.util.List<ValueAddedDeliveryOrderStatus> transitions = new java.util.ArrayList<>();
        for (ValueAddedDeliveryOrderStatus status : values()) {
            if (this.canTransitionTo(status)) {
                transitions.add(status);
            }
        }
        return transitions;
    }
}
