package com.bxm.customer.controller;

import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusTransitionRequest;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.query.valueAdded.DeliveryOrderQuery;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedItemTypeVO;
import com.bxm.customer.service.impl.ValueAddedStatusMachineService;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 增值交付单Controller
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valuedAddedDeliveryOrder")
@Api(tags = "增值交付单管理")
public class ValuedAddedDeliveryOrderController extends BaseController {

    @Autowired
    private IValueAddedDeliveryOrderService valueAddedDeliveryOrderService;

    @Autowired
    private IValueAddedItemTypeService valueAddedItemTypeService;

    /**
     * 新增或更新增值交付单
     *
     * @param orderVO 增值交付单VO
     * @return 操作结果
     */
    @PostMapping("/upsert")
    @ApiOperation(value = "新增或更新增值交付单", notes = "支持新增和更新操作，根据ID或交付单编号自动判断")
    @Log(title = "Upsert value added delivery order", businessType = BusinessType.INSERT)
    public AjaxResult upsert(@Valid @RequestBody ValueAddedDeliveryOrderVO orderVO) {
        try {
            log.info("Upsert delivery order request: {}", orderVO.getCustomerName());
            // 调用服务层进行upsert操作
            ValueAddedDeliveryOrder result = valueAddedDeliveryOrderService.upsert(orderVO);
            // 转换为VO返回
            ValueAddedDeliveryOrderVO resultVO = new ValueAddedDeliveryOrderVO();
            BeanUtils.copyProperties(result, resultVO);
            log.info("Upsert delivery order success: {}", result.getDeliveryOrderNo());
            return success(resultVO);
        } catch (IllegalArgumentException e) {
            log.warn("Upsert delivery order validation failed: {}", e.getMessage());
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("Upsert delivery order failed for customer: {}", orderVO.getCustomerName(), e);
            return error("保存增值交付单失败");
        }
    }

    /**
     * 条件查询增值交付单
     * 所有条件在 service 层进行动态拼接
     */
    @GetMapping("/query")
    @ApiOperation(value = "增值交付单查询", notes = "按条件分页查询增值交付单；条件均为可选")
    @Log(title = "Query value added delivery order", businessType = BusinessType.OTHER)
    public TableDataInfo query(DeliveryOrderQuery query) {
        // 启动分页（从请求参数 pageNum/pageSize 注入）
        startPage();
        // 记录查询关键信息，避免日志过大
        log.info("Query delivery orders, deliveryOrderNo={}, customerName={}, itemTypeId={}, status={}",
                StringUtils.nvl(query.getDeliveryOrderNo(), ""),
                StringUtils.nvl(query.getCustomerName(), ""),
                query.getValueAddedItemTypeId(),
                StringUtils.nvl(query.getStatus(), ""));
        List<ValueAddedDeliveryOrderVO> list = valueAddedDeliveryOrderService.queryVO(query);
        return getDataTable(list);
    }

    /**
     * 根据交付单编号查询增值交付单
     *
     * @param deliveryOrderNo 交付单编号
     * @return 查询结果
     */
    @GetMapping("/getByOrderNo/{deliveryOrderNo}")
    @ApiOperation(value = "根据交付单编号查询", notes = "根据交付单编号查询增值交付单详情")
    @Log(title = "Get delivery order by order number", businessType = BusinessType.OTHER)
    public AjaxResult getByOrderNo(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Query delivery order by order number: {}", deliveryOrderNo);
            ValueAddedDeliveryOrder order = valueAddedDeliveryOrderService.getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                return error("未找到对应的增值交付单");
            }
            ValueAddedDeliveryOrderVO resultVO = new ValueAddedDeliveryOrderVO();
            BeanUtils.copyProperties(order, resultVO);
            return success(resultVO);
        } catch (Exception e) {
            log.error("Query delivery order failed for order number: {}", deliveryOrderNo, e);
            return error("查询增值交付单失败");
        }
    }

    /**
     * 生成交付单编号
     * 编号规则：VAD + yyMMddHHmmsss + 3位随机码，总长度19位
     */
    @GetMapping("/genDeliveryOrderNo")
    @ApiOperation(value = "生成交付单编号", notes = "生成唯一的增值交付单编号，格式：VAD+时间戳到毫秒+随机码，总长度19位")
    @Log(title = "Generate delivery order number", businessType = BusinessType.OTHER)
    public AjaxResult genDeliveryOrderNo() {
        try {
            // 生成时间戳部分：yyMMddHHmmsss格式（13位）
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmsss"));
            // 生成3位随机码
            String randomCode = StringUtils.generateRandomCode(3);
            // 组合生成最终编号
            String deliveryOrderNo = "VAD" + timestamp + randomCode;
            log.info("Generated delivery order number: {}", deliveryOrderNo);
            return success(deliveryOrderNo);
        } catch (Exception e) {
            log.error("Failed to generate delivery order number", e);
            return error("生成交付单编号失败");
        }
    }

    /**
     * 查询增值事项类型列表
     *
     * @return 增值事项类型列表
     */
    @GetMapping("/listItemType")
    @ApiOperation(value = "查询增值事项类型列表", notes = "获取所有可用的增值事项类型")
    @Log(title = "List value added item types", businessType = BusinessType.OTHER)
    public AjaxResult listItemType() {
        try {
            log.info("Query value added item type list");
            List<ValueAddedItemTypeVO> itemTypes = valueAddedItemTypeService.listItemTypeVO();
            log.info("Query value added item type list success, count: {}", itemTypes.size());
            return success(itemTypes);
        } catch (Exception e) {
            log.error("Query value added item type list failed", e);
            return error("查询增值事项类型列表失败");
        }
    }

    /**
     * 修改增值交付单状态
     *
     * @param request 状态转换请求
     * @return 操作结果
     */
    @PostMapping("/modifyStatus")
    @ApiOperation(value = "修改增值交付单状态", notes = "支持完整的状态机转换，包括状态验证、业务规则检查等")
    @Log(title = "Modify delivery order status", businessType = BusinessType.UPDATE)
    public AjaxResult modifyStatus(@Valid @RequestBody StatusTransitionRequest request) {
        try {
            log.info("Modify delivery order status request: orderNo={}, targetStatus={}, operationCode={}",
                    request.getDeliveryOrderNo(), request.getTargetStatus(), request.getOperationCode());

            // 调用服务层执行状态转换
            ValueAddedStatusMachineService.StatusTransitionResult result = valueAddedDeliveryOrderService.modifyStatus(request);

            if (result.isSuccess()) {
                log.info("Modify delivery order status success: {} -> {} for order {}",
                        result.getOldStatus(), result.getNewStatus(), request.getDeliveryOrderNo());
                return success(result.getMessage(), result);
            } else {
                log.warn("Modify delivery order status failed: {} for order {}",
                        result.getMessage(), request.getDeliveryOrderNo());
                return error(result.getMessage());
            }

        } catch (IllegalArgumentException e) {
            log.warn("Modify delivery order status validation failed: {}", e.getMessage());
            return error(e.getMessage());
        } catch (Exception e) {
            log.error("Modify delivery order status failed for order: {}", request.getDeliveryOrderNo(), e);
            return error("修改交付单状态失败");
        }
    }

    /**
     * 查询交付单可用的状态转换
     *
     * @param deliveryOrderNo 交付单编号
     * @return 可用的状态转换列表
     */
    @GetMapping("/getAvailableTransitions/{deliveryOrderNo}")
    @ApiOperation(value = "查询可用状态转换", notes = "根据当前状态查询可以转换到的目标状态列表")
    @Log(title = "Get available status transitions", businessType = BusinessType.OTHER)
    public AjaxResult getAvailableTransitions(@PathVariable String deliveryOrderNo) {
        try {
            log.info("Query available transitions for delivery order: {}", deliveryOrderNo);

            // 查询交付单
            ValueAddedDeliveryOrder order = valueAddedDeliveryOrderService.getByDeliveryOrderNo(deliveryOrderNo);
            if (order == null) {
                return error("未找到对应的增值交付单");
            }

            // 获取当前状态
            ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
            if (currentStatus == null) {
                return error("当前状态无效: " + order.getStatus());
            }

            // 获取可用转换
            List<ValueAddedDeliveryOrderStatus> availableTransitions = currentStatus.getAvailableTransitions();

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("currentStatus", currentStatus);
            result.put("availableTransitions", availableTransitions);
            result.put("deliveryOrderNo", deliveryOrderNo);

            log.info("Query available transitions success for order: {}, current status: {}, available count: {}",
                    deliveryOrderNo, currentStatus.getCode(), availableTransitions.size());
            return success(result);

        } catch (Exception e) {
            log.error("Query available transitions failed for order: {}", deliveryOrderNo, e);
            return error("查询可用状态转换失败");
        }
    }
}
