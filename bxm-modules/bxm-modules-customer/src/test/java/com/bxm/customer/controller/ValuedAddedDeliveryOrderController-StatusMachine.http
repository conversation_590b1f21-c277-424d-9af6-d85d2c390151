### 增值交付单状态机测试文件
### 测试ValuedAddedDeliveryOrderController的状态转换功能
### 基于图中状态流转设计的完整测试场景

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjQxYjljZjljLWJjZTMtNDY0Yy04ODAyLWQzNDJiY2QyNDQzMCIsInVzZXJuYW1lIjoiYWRtaW4ifQ.AabdDup8QE3j4ybMkj_qPKPFqNucYH4KGIyZ6NsIiBX5bgOtSjhDvikmkuLwvidHWwcu_zS4RFLv9DIHvxrDXQ

### ========================================
### 1. 查询可用状态转换 - 草稿状态
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAvailableTransitions/VAD2508051430001A1C
Authorization: {{authorization}}

### ========================================
### 2. 状态转换：草稿 -> 已保存待提交 (added:submit)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "SAVED_PENDING_SUBMIT",
  "operationCode": "added:submit",
  "reason": "用户提交交付单保存",
  "forceTransition": false
}

### ========================================
### 3. 状态转换：已保存待提交 -> 已提交待交付 (added:newSubmit)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "operationCode": "added:newSubmit",
  "reason": "正式提交交付单",
  "forceTransition": false
}

### ========================================
### 4. 状态转换：已提交待交付 -> 已交付待确认 (added:deliverSubmit)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "PENDING_CONFIRMATION",
  "operationCode": "added:deliverSubmit",
  "reason": "交付材料已提交，等待确认",
  "forceTransition": false
}

### ========================================
### 5. 状态转换：已交付待确认 -> 已确认待扣款 (added:change)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "CONFIRMED_PENDING_DEDUCTION",
  "operationCode": "added:change",
  "reason": "交付材料确认通过，准备扣款",
  "forceTransition": false
}

### ========================================
### 6. 状态转换：已确认待扣款 -> 已扣款待确认 (added:deduction)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "DEDUCTION_PENDING_CONFIRMATION",
  "operationCode": "added:deduction",
  "reason": "扣款处理完成，等待确认",
  "forceTransition": false
}

### ========================================
### 7. 状态转换：已扣款待确认 -> 已扣款 (added:deductionSubmit)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "DEDUCTION_COMPLETED",
  "operationCode": "added:deductionSubmit",
  "reason": "扣款确认完成",
  "forceTransition": false
}

### ========================================
### 8. 测试异常场景：尝试从终态转换
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "DRAFT",
  "operationCode": "added:back",
  "reason": "尝试从终态回退（应该失败）",
  "forceTransition": false
}

### ========================================
### 9. 测试强制转换
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "DRAFT",
  "operationCode": "added:back",
  "reason": "强制回退到草稿状态",
  "forceTransition": true
}

### ========================================
### 10. 测试退回操作：已提交待交付 -> 已保存待提交 (added:back)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430002C2D",
  "targetStatus": "SAVED_PENDING_SUBMIT",
  "operationCode": "added:back",
  "reason": "交付材料需要修改，退回重新提交",
  "forceTransition": false
}

### ========================================
### 11. 测试异常处理：交付异常 -> 关闭交付 (added:closeDeliver)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430003E3F",
  "targetStatus": "DELIVERY_CLOSED",
  "operationCode": "added:closeDeliver",
  "reason": "交付异常，关闭交付流程",
  "forceTransition": false
}

### ========================================
### 12. 测试无效参数
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "",
  "targetStatus": "INVALID_STATUS",
  "operationCode": "invalid:operation",
  "reason": "测试无效参数",
  "forceTransition": false
}

### ========================================
### 13. 查询不存在的交付单状态转换
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/getAvailableTransitions/INVALID_ORDER_NO
Authorization: {{authorization}}

### ========================================
### 14. 测试补充交付材料：已提交待交付 -> 已交付待确认 (added:supply)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430004F4G",
  "targetStatus": "PENDING_CONFIRMATION",
  "operationCode": "added:supply",
  "reason": "补充交付材料",
  "forceTransition": false,
  "additionalData": "{\"supplementFiles\": [\"file1.pdf\", \"file2.xlsx\"]}"
}

### ========================================
### 15. 测试确认操作：已交付待确认 -> 待确认 (added:check)
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/modifyStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430005G5H",
  "targetStatus": "PENDING_REVIEW",
  "operationCode": "added:check",
  "reason": "进入确认流程",
  "forceTransition": false
}
